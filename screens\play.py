# play_screen.py
import pygame
import math
import random
from settings import *
from screens.entities import Player
from screens.spawner import Spawner

class PlayScreen:
    def __init__(self, game):
        """
        <PERSON><PERSON><PERSON> ch<PERSON> (PlayScreen)
        game: object chứa ít nhất
          - screen, clock
          - images: obstacle_imgs, coin_img, treasure_img, tree_imgs, monster_img, player_img
        """
        self.game = game
        self.screen = game.screen
        self.clock = game.clock

        # Background system - multiple backgrounds for map switching
        self.background_paths = [
            "resources/assets/backgrounds/test.jpg",
            "resources/assets/backgrounds/bg_game.jpg",
            "resources/assets/backgrounds/bg_startgame.jpg",
            "resources/assets/backgrounds/bg_startgame2.jpg"
        ]
        self.current_bg_index = 0
        self.background = self.load_background(self.background_paths[self.current_bg_index])
        self.wave_offset = 0     # hiệu ứng sóng ngang
        self.scroll_y = 0        # hiệu ứng cuộn dọc

        # Map switching and night mode system
        self.game_start_time = 0  # Th<PERSON><PERSON> <PERSON><PERSON> b<PERSON><PERSON> đầu game thực sự (sau countdown)
        self.map_switch_interval = 60000  # 1 phút = 60000ms
        self.last_map_switch = 0
        self.night_mode = False
        self.night_overlay_alpha = 0  # Độ trong suốt của overlay tối
        self.target_night_alpha = 0

        # Map switch notification system
        self.show_map_notification = False
        self.map_notification_timer = 0
        self.map_notification_duration = 3000  # 3 seconds

        # Nút back (chỉ hiện trong countdown)
        self.back_button = pygame.Rect(30, 30, 60, 60)
        self.back_icon = pygame.image.load("resources/assets/icon/return_icon.png").convert_alpha()
        self.back_icon = pygame.transform.smoothscale(self.back_icon, (32, 32))

        # Countdown trước khi chơi
        self.countdown = 3
        self.countdown_timer = 0

        # Sprite groups
        self.obstacles = pygame.sprite.Group()
        self.coins = pygame.sprite.Group()
        self.treasures = pygame.sprite.Group()
        self.trees = pygame.sprite.Group()
        self.monsters = pygame.sprite.Group()

        groups = {
            'obstacles': self.obstacles,
            'coins': self.coins,
            'treasures': self.treasures,
            'trees': self.trees,
            'monsters': self.monsters
        }

        # Ảnh vật thể (lấy từ game hoặc None)
        images = {
            'obstacles': getattr(game, 'obstacle_imgs', []),
            'coin': getattr(game, 'coin_img', None),
            'treasure': getattr(game, 'treasure_img', None),
            'trees': getattr(game, 'tree_imgs', []),
            'monster': getattr(game, 'monster_img', None)
        }

        # Player
        player_img = getattr(game, 'player_img', "resources/assets/characters/player.png")
        self.player = Player(player_img,
                             start_x=WIDTH//2, start_y=-200,
                             target_y=int(HEIGHT*0.62),
                             size=(150,150), drop_speed=9)
        self.player_group = pygame.sprite.GroupSingle(self.player)

        # Spawner
        self.spawner = Spawner(game, groups, images)

        # Gameplay
        self.running = False
        self.score = 0

    # ---------------- Background Management ----------------
    def load_background(self, path):
        """Load và scale background image"""
        try:
            bg = pygame.image.load(path).convert()
            return pygame.transform.scale(bg, (WIDTH, HEIGHT))
        except pygame.error:
            # Fallback to test.jpg if loading fails
            bg = pygame.image.load("resources/assets/backgrounds/test.jpg").convert()
            return pygame.transform.scale(bg, (WIDTH, HEIGHT))

    def switch_to_next_map(self):
        """Chuyển sang map tiếp theo"""
        self.current_bg_index = (self.current_bg_index + 1) % len(self.background_paths)
        self.background = self.load_background(self.background_paths[self.current_bg_index])

        # Kích hoạt thông báo chuyển map
        self.show_map_notification = True
        self.map_notification_timer = 0
        print(f"Switched to map: {self.background_paths[self.current_bg_index]}")

    def toggle_night_mode(self):
        """Bật/tắt chế độ tối"""
        self.night_mode = not self.night_mode
        self.target_night_alpha = 120 if self.night_mode else 0
        print(f"Night mode: {'ON' if self.night_mode else 'OFF'}")

    def update_night_overlay(self, dt):
        """Cập nhật hiệu ứng overlay tối"""
        if self.night_overlay_alpha != self.target_night_alpha:
            fade_speed = 100  # pixels per second
            if self.night_overlay_alpha < self.target_night_alpha:
                self.night_overlay_alpha = min(self.target_night_alpha,
                                             self.night_overlay_alpha + fade_speed * dt / 1000)
            else:
                self.night_overlay_alpha = max(self.target_night_alpha,
                                             self.night_overlay_alpha - fade_speed * dt / 1000)

    # ---------------- Vẽ nền ----------------
    def draw_background(self):
        """Vẽ nền với hiệu ứng sóng ngang + cuộn dọc + night overlay"""
        wave_surface = pygame.Surface((WIDTH, HEIGHT))
        for y in range(HEIGHT):
            shift = int(10 * math.sin(y/30 + self.wave_offset*0.02))
            line = self.background.subsurface((0, y, WIDTH, 1))
            wave_surface.blit(line, (shift, y))
            if shift > 0:
                wave_surface.blit(line, (shift-WIDTH, y))
            elif shift < 0:
                wave_surface.blit(line, (shift+WIDTH, y))

        sy = self.scroll_y % HEIGHT
        self.screen.blit(wave_surface, (0, sy-HEIGHT))
        self.screen.blit(wave_surface, (0, sy))

        # Vẽ night overlay nếu có
        if self.night_overlay_alpha > 0:
            night_overlay = pygame.Surface((WIDTH, HEIGHT))
            # Tạo hiệu ứng gradient tối với nhiều màu sắc
            if self.current_bg_index % 2 == 0:
                night_overlay.fill((0, 0, 50))  # Màu xanh đậm
            else:
                night_overlay.fill((30, 0, 30))  # Màu tím đậm
            night_overlay.set_alpha(int(self.night_overlay_alpha))
            self.screen.blit(night_overlay, (0, 0))

    def draw_back_button(self):
        """Vẽ nút quay lại (trong countdown)"""
        if self.countdown > 0:
            btn_surf = pygame.Surface(self.back_button.size, pygame.SRCALPHA)
            pygame.draw.rect(btn_surf, (0,0,0,120), btn_surf.get_rect(), border_radius=15)
            btn_surf.blit(self.back_icon,
                          (self.back_button.width//2 - self.back_icon.get_width()//2,
                           self.back_button.height//2 - self.back_icon.get_height()//2))
            self.screen.blit(btn_surf, self.back_button.topleft)

    def draw_countdown(self):
        """Vẽ đồng hồ đếm ngược"""
        if self.countdown > 0:
            pulse = 1.0 + 0.12 * math.sin(pygame.time.get_ticks() * 0.01)
            size = int(200 * pulse)
            font = pygame.font.SysFont("Arial", size, bold=True)
            text = font.render(str(self.countdown), True, (255,80,40))
            shadow = font.render(str(self.countdown), True, (0,0,0))
            rect = text.get_rect(center=(WIDTH//2, HEIGHT//2))
            self.screen.blit(shadow, (rect.x+6, rect.y+6))
            self.screen.blit(text, rect)

    def draw_score(self):
        """Vẽ điểm số và thông tin game"""
        f = pygame.font.SysFont("Arial", 30, bold=True)
        txt = f.render(f"Score: {self.score}  Coins: {self.player.coins_collected}", True, (255,255,255))
        self.screen.blit(txt, (WIDTH-240, 20))

        # Hiển thị thời gian và map hiện tại (chỉ khi đã bắt đầu chơi)
        if self.countdown <= 0 and self.game_start_time > 0:
            current_time = pygame.time.get_ticks()
            elapsed_time = (current_time - self.game_start_time) // 1000  # seconds
            time_to_next_switch = 60 - (elapsed_time % 60)  # seconds until next switch

            f_small = pygame.font.SysFont("Arial", 20, bold=True)
            time_txt = f_small.render(f"Time: {elapsed_time}s | Next map in: {time_to_next_switch}s", True, (255,255,255))
            self.screen.blit(time_txt, (WIDTH-300, 60))

            # Hiển thị trạng thái night mode
            mode_txt = f_small.render(f"Night Mode: {'ON' if self.night_mode else 'OFF'}", True, (255,255,100))
            self.screen.blit(mode_txt, (WIDTH-200, 90))

            # Hiển thị tên map hiện tại
            current_map_name = self.background_paths[self.current_bg_index].split('/')[-1].replace('.jpg', '')
            map_name_txt = f_small.render(f"Current Map: {current_map_name}", True, (255,255,255))
            self.screen.blit(map_name_txt, (WIDTH-250, 120))

            # Hiển thị hướng dẫn phím
            f_tiny = pygame.font.SysFont("Arial", 16, bold=True)
            help_txt1 = f_tiny.render("Press M: Switch Map | Press N: Toggle Night Mode", True, (200,200,200))
            self.screen.blit(help_txt1, (WIDTH-350, 150))

    def draw_map_notification(self):
        """Vẽ thông báo chuyển map"""
        if self.show_map_notification:
            # Tạo hiệu ứng fade in/out
            progress = self.map_notification_timer / self.map_notification_duration
            if progress < 0.2:  # Fade in
                alpha = int(255 * (progress / 0.2))
            elif progress > 0.8:  # Fade out
                alpha = int(255 * ((1.0 - progress) / 0.2))
            else:  # Full opacity
                alpha = 255

            # Tạo surface cho thông báo
            notification_surface = pygame.Surface((400, 100), pygame.SRCALPHA)
            notification_surface.fill((0, 0, 0, 150))  # Background semi-transparent

            # Text thông báo
            font_big = pygame.font.SysFont("Arial", 36, bold=True)
            font_small = pygame.font.SysFont("Arial", 24, bold=True)

            map_name = self.background_paths[self.current_bg_index].split('/')[-1].replace('.jpg', '')
            title_text = font_big.render("MAP CHANGED!", True, (255, 255, 0))
            map_text = font_small.render(f"Now playing: {map_name}", True, (255, 255, 255))
            night_text = font_small.render(f"Night Mode: {'ON' if self.night_mode else 'OFF'}", True, (255, 255, 100))

            # Vẽ text lên notification surface
            notification_surface.blit(title_text, (200 - title_text.get_width()//2, 10))
            notification_surface.blit(map_text, (200 - map_text.get_width()//2, 45))
            notification_surface.blit(night_text, (200 - night_text.get_width()//2, 70))

            # Set alpha và vẽ lên screen
            notification_surface.set_alpha(alpha)
            self.screen.blit(notification_surface, (WIDTH//2 - 200, HEIGHT//2 - 50))

    # ---------------- Va chạm ----------------
    def handle_collisions(self):
        """Xử lý va chạm & luật chơi"""
        # Ăn coin
        coins_hit = pygame.sprite.spritecollide(
            self.player, self.coins, dokill=True, collided=pygame.sprite.collide_mask
        )
        if coins_hit:
            self.player.coins_collected += len(coins_hit)
            self.score += 5 * len(coins_hit)

        # Ăn treasure
        treasures_hit = pygame.sprite.spritecollide(
            self.player, self.treasures, dokill=True, collided=pygame.sprite.collide_mask
        )
        if treasures_hit:
            self.score += 50 * len(treasures_hit)

        # Đụng obstacle = thua
        if pygame.sprite.spritecollideany(
            self.player, self.obstacles, collided=pygame.sprite.collide_mask
        ):
            self.game.state = "game_over"
            return True

        # Đụng tree = spawn monster
        # Đụng tree = spawn monster
        trees_hit = pygame.sprite.spritecollide(
            self.player, self.trees, dokill=False, collided=pygame.sprite.collide_mask
        )
        for t in trees_hit:
            if not getattr(t, 'called_monster', False):
                t.called_monster = True
                self.spawner.spawn_monster_from_tree(t, self.player)

        # Player đụng trực tiếp monster = thua
        if pygame.sprite.spritecollideany(
            self.player, self.monsters, collided=pygame.sprite.collide_mask
        ):
            self.game.state = "game_over"
            return True


        return False


    # ---------------- Loop chính ----------------
    def run(self):
        self.running = True
        while self.running and self.game.running:
            dt = self.clock.tick(FPS)
            self.wave_offset += 2
            self.scroll_y += 1

            # Countdown
            if self.countdown > 0:
                self.countdown_timer += dt
                if self.countdown_timer >= 1000:
                    self.countdown -= 1
                    self.countdown_timer = 0
                    # Bắt đầu tính thời gian game khi countdown kết thúc
                    if self.countdown == 0:
                        self.game_start_time = pygame.time.get_ticks()
                        self.last_map_switch = self.game_start_time

            # Map switching logic (chỉ khi đã bắt đầu chơi)
            if self.countdown <= 0 and self.game_start_time > 0:
                current_time = pygame.time.get_ticks()
                time_since_last_switch = current_time - self.last_map_switch

                # Chuyển map mỗi 1 phút
                if time_since_last_switch >= self.map_switch_interval:
                    self.switch_to_next_map()
                    self.toggle_night_mode()  # Bật/tắt night mode khi chuyển map
                    self.last_map_switch = current_time

            # Cập nhật night overlay
            self.update_night_overlay(dt)

            # Cập nhật map notification timer
            if self.show_map_notification:
                self.map_notification_timer += dt
                if self.map_notification_timer >= self.map_notification_duration:
                    self.show_map_notification = False

            # Event
            for e in pygame.event.get():
                if e.type == pygame.QUIT:
                    self.running = False
                    self.game.running = False
                elif e.type == pygame.KEYDOWN:
                    if e.key == pygame.K_ESCAPE:
                        self.game.state = "start"
                        self.running = False
                    elif e.key == pygame.K_m and self.countdown <= 0:
                        # Chuyển map thủ công bằng phím M
                        self.switch_to_next_map()
                        self.last_map_switch = pygame.time.get_ticks()  # Reset timer
                    elif e.key == pygame.K_n and self.countdown <= 0:
                        # Bật/tắt night mode bằng phím N
                        self.toggle_night_mode()
                elif e.type == pygame.MOUSEBUTTONDOWN and self.countdown > 0:
                    if self.back_button.collidepoint(e.pos):
                        self.game.state = "start"
                        self.running = False

            # Vẽ nền
            self.draw_background()

            # Update / spawn
            if self.countdown <= 0:
                self.spawner.maybe_spawn_every_frame(dt)
                self.obstacles.update(dt, 0)
                self.coins.update(dt, 0)
                self.treasures.update(dt, 0)
                self.trees.update(dt, 0)
                self.monsters.update(dt, 0)
            else:
                # countdown thì update nhẹ thôi
                self.obstacles.update(dt, 0)
                self.coins.update(dt, 0)
                self.treasures.update(dt, 0)
                self.trees.update(dt, 0)
                self.monsters.update(dt, 0)

            # Update player (luôn hoạt động để hiệu ứng rơi xuống xuất hiện)
            mouse_pos = pygame.mouse.get_pos()
            self.player.update(dt, mouse_pos)

            # Vẽ sprites
            self.obstacles.draw(self.screen)
            self.trees.draw(self.screen)
            self.coins.draw(self.screen)
            self.treasures.draw(self.screen)
            self.monsters.draw(self.screen)
            self.player_group.draw(self.screen)

            # HUD
            self.draw_back_button()
            self.draw_countdown()
            self.draw_score()
            self.draw_map_notification()

            # Va chạm (sau khi countdown xong)
            if self.countdown <= 0:
                if self.handle_collisions():
                    self.running = False

            pygame.display.flip()
