# Map Switching & Night Mode Features

## Tính năng mới đã thêm vào game:

### 1. Chuyển Map Tự Động
- **Thời gian**: Map sẽ tự động chuyển sau mỗi **1 phút** (60 giây) chơi
- **Maps có sẵn**:
  - `test.jpg` (map mặc định)
  - `bg_game.jpg`
  - `bg_startgame.jpg`
  - `bg_startgame2.jpg`
- **<PERSON> kỳ**: Maps sẽ chuyển theo vòng tròn, sau map cuối sẽ quay lại map đầu

### 2. Chế Độ Tối (Night Mode)
- **Tự động**: Khi chuyển map, chế độ tối sẽ tự động bật/tắt xen kẽ
- **Hiệu ứng**: Overlay màu xanh đậm với độ trong suốt 120/255
- **Fade**: Hiệu <PERSON>ng fade in/out mượt mà khi chuyển đổi

### 3. <PERSON><PERSON><PERSON><PERSON>hiển Thủ Công
- **Phím M**: Chuyển map ngay lập tức (chỉ hoạt động khi đã bắt đầu chơi)
- **Phím N**: Bật/tắt chế độ tối ngay lập tức
- **Reset Timer**: Khi chuyển map thủ công, timer tự động sẽ được reset

### 4. Thông Báo Trực Quan
- **Popup thông báo**: Hiển thị khi chuyển map với hiệu ứng fade
- **Thời gian hiển thị**: 3 giây
- **Thông tin**: Tên map mới và trạng thái night mode
- **Vị trí**: Giữa màn hình

### 5. HUD Thông Tin
- **Timer**: Hiển thị thời gian đã chơi và thời gian còn lại đến lần chuyển map tiếp theo
- **Night Mode Status**: Hiển thị trạng thái ON/OFF
- **Hướng dẫn phím**: Hiển thị cách sử dụng phím M và N

## Cách sử dụng:

### Chơi bình thường:
```bash
python main.py
```
- Map sẽ tự động chuyển sau mỗi 60 giây
- Sử dụng phím M và N để điều khiển thủ công

### Test nhanh (map chuyển mỗi 10 giây):
```bash
python test_map_switch.py
```
- Map sẽ chuyển sau mỗi 10 giây để test nhanh hơn

## Cấu trúc Code:

### Các thuộc tính mới trong PlayScreen:
- `background_paths`: Danh sách đường dẫn các background
- `current_bg_index`: Index của background hiện tại
- `game_start_time`: Thời gian bắt đầu game
- `map_switch_interval`: Khoảng thời gian giữa các lần chuyển map
- `night_mode`: Trạng thái chế độ tối
- `night_overlay_alpha`: Độ trong suốt của overlay tối
- `show_map_notification`: Hiển thị thông báo chuyển map

### Các phương thức mới:
- `load_background()`: Load và scale background image
- `switch_to_next_map()`: Chuyển sang map tiếp theo
- `toggle_night_mode()`: Bật/tắt chế độ tối
- `update_night_overlay()`: Cập nhật hiệu ứng overlay tối
- `draw_map_notification()`: Vẽ thông báo chuyển map

## Lưu ý:
- Tính năng chỉ hoạt động sau khi countdown kết thúc
- Phím điều khiển chỉ hoạt động khi đã bắt đầu chơi
- Game sẽ fallback về `test.jpg` nếu không load được background khác
- Timer sẽ được reset khi chuyển map thủ công
