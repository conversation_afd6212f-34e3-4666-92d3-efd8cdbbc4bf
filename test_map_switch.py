#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng chuyển map nhanh hơn
Chuyển map mỗi 10 giây thay vì 60 giây
"""

import pygame
import sys
import os

# Th<PERSON><PERSON> thư mục hiện tại vào path để import đ<PERSON><PERSON><PERSON> các module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game import Game

class TestGame(Game):
    """Game class với map switching nhanh hơn để test"""
    def __init__(self):
        super().__init__()
    
    def run(self):
        from screens.start import StartScreen
        from screens.play import PlayScreen
        from screens.gameover import GameOverScreen
        
        # Tạo PlayScreen với map switch interval ngắn hơn
        class TestPlayScreen(PlayScreen):
            def __init__(self, game):
                super().__init__(game)
                # Chuyển map mỗi 10 giây thay vì 60 giây
                self.map_switch_interval = 10000  # 10 seconds
                print("Test mode: Map will switch every 10 seconds!")
        
        start_screen = StartScreen(self)
        play_screen = TestPlayScreen(self)  # Sử dụng TestPlayScreen
        game_over_screen = GameOverScreen(self)

        while self.running:
            if self.state == "start":
                start_screen.run()
            elif self.state == "play":
                play_screen.run()
            elif self.state == "game_over":
                game_over_screen.run()

        pygame.quit()

if __name__ == "__main__":
    print("Starting test game with fast map switching...")
    print("Maps will change every 10 seconds instead of 60 seconds")
    print("Press ESC to return to start screen, or close window to quit")
    
    test_game = TestGame()
    test_game.run()
